/**
 * Web UI 樣式
 * ===========
 * 
 * 補充樣式和動畫效果
 */

/* 連接狀態指示器 */
.connection-indicator {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.connection-indicator.connected {
    background: rgba(76, 175, 80, 0.1);
    color: #4caf50;
    border: 1px solid #4caf50;
}

.connection-indicator.disconnected {
    background: rgba(244, 67, 54, 0.1);
    color: #f44336;
    border: 1px solid #f44336;
}

/* 載入動畫 */
.loading {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #464647;
    border-radius: 50%;
    border-top-color: #007acc;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 淡入動畫 */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 滑入動畫 */
.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* 脈衝動畫 */
.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* 工具提示 */
.tooltip {
    position: relative;
    cursor: help;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--bg-primary);
    color: var(--text-primary);
    padding: 8px 12px;
    border-radius: 4px;
    border: 1px solid var(--border-color);
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
    z-index: 1000;
}

/* ===== 可折疊統計面板 ===== */
.stats-panel-floating {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    z-index: 100;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform;
}

/* 收起狀態 */
.stats-panel-floating.collapsed {
    transform: translateY(calc(100% - 40px));
}

/* 統計面板頭部 */
.stats-panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    cursor: pointer;
    user-select: none;
    background: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-color);
}

.stats-panel-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    font-weight: 500;
    color: var(--text-primary);
}

.stats-toggle-icon {
    transition: transform 0.3s ease;
}

.stats-panel-floating.collapsed .stats-toggle-icon {
    transform: rotate(180deg);
}

/* 統計面板內容 */
.stats-panel-content {
    padding: 16px 20px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    max-width: 1200px;
    margin: 0 auto;
}

/* 統計項目 */
.stats-item-detailed {
    display: flex;
    flex-direction: column;
    gap: 4px;
    padding: 12px;
    background: var(--bg-primary);
    border-radius: 6px;
    border: 1px solid var(--border-color);
}

.stats-item-label {
    font-size: 11px;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stats-item-value {
    font-size: 16px;
    font-weight: 600;
    color: var(--accent-color);
    font-family: 'Consolas', 'Monaco', monospace;
}


/* 動畫效能優化 */
@media (prefers-reduced-motion: reduce) {
    .stats-panel-floating {
        transition: none;
    }
    
    .stats-toggle-icon {
        transition: none;
    }
}

/* 響應式設計 */
@media (max-width: 768px) {
    .stats-panel-content {
        grid-template-columns: 1fr;
        gap: 12px;
        padding: 12px 16px;
    }
    
    .stats-item-detailed {
        padding: 10px;
    }
}

.tooltip:hover::after {
    opacity: 1;
}

/* 滾動條美化 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-tertiary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #606060;
}

/* 選擇文字顏色 */
::selection {
    background: rgba(0, 122, 204, 0.3);
    color: var(--text-primary);
}

/* 無障礙改進 */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* 焦點可見性 */
button:focus-visible,
input:focus-visible,
textarea:focus-visible,
select:focus-visible {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}

/* 禁用狀態 */
button:disabled,
input:disabled,
textarea:disabled,
select:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 響應式圖片 */
img {
    max-width: 100%;
    height: auto;
}

/* 表格樣式 */
table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

th, td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

th {
    background: var(--bg-tertiary);
    font-weight: 600;
    color: var(--text-primary);
}

tr:hover {
    background: rgba(255, 255, 255, 0.02);
}

/* 代碼區塊 */
code {
    background: var(--bg-tertiary);
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 0.9em;
}

pre {
    background: var(--bg-tertiary);
    padding: 16px;
    border-radius: 6px;
    overflow-x: auto;
    border: 1px solid var(--border-color);
}

pre code {
    background: none;
    padding: 0;
}

/* 警告和提示框 */
.alert {
    padding: 12px 16px;
    border-radius: 6px;
    margin-bottom: 16px;
    border-left: 4px solid;
}

.alert-info {
    background: rgba(33, 150, 243, 0.1);
    border-left-color: var(--info-color);
    color: #bbdefb;
}

.alert-success {
    background: rgba(76, 175, 80, 0.1);
    border-left-color: var(--success-color);
    color: #c8e6c9;
}

.alert-warning {
    background: rgba(255, 152, 0, 0.1);
    border-left-color: var(--warning-color);
    color: #ffe0b2;
}

.alert-error {
    background: rgba(244, 67, 54, 0.1);
    border-left-color: var(--error-color);
    color: #ffcdd2;
}

/* 進度條 */
.progress {
    width: 100%;
    height: 8px;
    background: var(--bg-tertiary);
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: var(--accent-color);
    transition: width 0.3s ease;
}

/* 分隔線 */
.divider {
    height: 1px;
    background: var(--border-color);
    margin: 20px 0;
}

/* 徽章 */
.badge {
    display: inline-block;
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 500;
    border-radius: 12px;
    background: var(--accent-color);
    color: white;
}

.badge-secondary {
    background: var(--text-secondary);
}

.badge-success {
    background: var(--success-color);
}

.badge-warning {
    background: var(--warning-color);
}

.badge-error {
    background: var(--error-color);
}

/* 卡片 */
.card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.card-header {
    font-weight: 600;
    margin-bottom: 12px;
    color: var(--text-primary);
}

.card-body {
    color: var(--text-secondary);
}

/* 統計數字 */
.stat {
    text-align: center;
    padding: 20px;
}

.stat-value {
    font-size: 2em;
    font-weight: bold;
    color: var(--accent-color);
    display: block;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.9em;
    margin-top: 4px;
}

/* ===== Feedback.html 專用樣式 ===== */

/* CSS 變數定義 */
:root {
    /* 深色主題顏色變數 */
    --bg-primary: #1e1e1e;
    --bg-secondary: #2d2d30;
    --bg-tertiary: #252526;
    --surface-color: #333333;
    --text-primary: #cccccc;
    --text-secondary: #9e9e9e;
    --accent-color: #007acc;
    --accent-hover: #005a9e;
    --border-color: #464647;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --error-color: #f44336;
    --info-color: #2196f3;
}

/* 基礎重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 主體樣式 */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 容器樣式 */
.container {
    width: 100%;
    margin: 0 auto;
    padding: 12px;
    background: var(--bg-primary);
    color: var(--text-primary);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 頭部樣式 */
.header {
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    padding: 15px 0;
    margin-bottom: 20px;
    border-radius: 8px 8px 0 0;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 16px;
}

.title {
    font-size: 24px;
    font-weight: bold;
    color: var(--accent-color);
    margin: 0;
}

.project-info {
    color: var(--text-secondary);
    font-size: 14px;
}

/* 倒數計時器樣式 */
.countdown-display {
    display: flex;
    align-items: center;
    gap: 6px;
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 4px 8px;
    flex-shrink: 0;
}

.countdown-label {
    color: var(--text-secondary);
    font-size: 11px;
}

.countdown-timer {
    color: var(--warning-color);
    font-size: 13px;
    font-weight: bold;
    font-family: 'Consolas', 'Monaco', monospace;
    min-width: 45px;
    text-align: center;
}

.countdown-timer.warning {
    color: var(--warning-color);
}

.countdown-timer.danger {
    color: var(--error-color);
}





/* 語言選擇器 */
.language-selector {
    display: flex;
    align-items: center;
    gap: 10px;
}

.language-selector select {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 5px 10px;
    font-size: 14px;
}

/* 設定項目樣式 */
.setting-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 0;
    border-bottom: 1px solid var(--border-color);
}

.setting-item:last-child {
    border-bottom: none;
}

.setting-info {
    flex: 1;
}

.setting-label {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 16px;
    margin-bottom: 4px;
}

.setting-description {
    color: var(--text-secondary);
    font-size: 14px;
    line-height: 1.4;
}

/* 設定卡片樣式 */
.settings-card {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    margin-bottom: 20px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.3s ease;
}

.settings-card:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.settings-card-header {
    background: var(--bg-secondary);
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-color);
}

.settings-card-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.settings-card-body {
    padding: 20px;
}

.settings-card .setting-item {
    padding: 16px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.settings-card .setting-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.settings-card .setting-label {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 4px;
}

.settings-card .setting-description {
    font-size: 14px;
    color: var(--text-secondary);
    line-height: 1.4;
}

.settings-card .setting-warning {
    font-size: 12px;
    color: var(--warning-color);
    margin-top: 4px;
    font-weight: 500;
}

/* 圖片大小限制選擇器樣式 */
.image-size-limit-selector {
    display: flex;
    align-items: center;
}

.image-size-limit-select {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    color: var(--text-primary);
    padding: 8px 12px;
    font-size: 14px;
    min-width: 120px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.image-size-limit-select:hover {
    border-color: var(--accent-color);
    background: var(--bg-tertiary);
}

.image-size-limit-select:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.2);
}

/* Base64 切換器容器樣式 */
.base64-toggle-container {
    display: flex;
    align-items: center;
}

/* 現代化切換開關樣式 */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
    cursor: pointer;
}

.toggle-input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--border-color);
    border-radius: 24px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
}

.toggle-slider:before {
    content: "";
    position: absolute;
    height: 18px;
    width: 18px;
    left: 3px;
    background: white;
    border-radius: 50%;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.toggle-input:checked + .toggle-slider {
    background: var(--accent-color);
}

.toggle-input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

.toggle-switch:hover .toggle-slider {
    box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.2);
}

/* 佈局模式選擇器 */
.layout-mode-selector {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 8px;
}

.layout-option {
    display: flex;
    align-items: flex-start;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: var(--bg-tertiary);
    transition: all 0.3s ease;
    cursor: pointer;
}

.layout-option:hover {
    border-color: var(--accent-color);
    background: rgba(0, 122, 204, 0.1);
}

.layout-option input[type="radio"] {
    margin: 0;
    margin-right: 12px;
    margin-top: 2px;
    accent-color: var(--accent-color);
    transform: scale(1.2);
}

.layout-option label {
    flex: 1;
    cursor: pointer;
}

.layout-option-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.layout-option-desc {
    font-size: 12px;
    color: var(--text-secondary);
    line-height: 1.4;
}

.layout-option input[type="radio"]:checked + label {
    color: var(--accent-color);
}

.layout-option input[type="radio"]:checked + label .layout-option-title {
    color: var(--accent-color);
}

.layout-option:has(input[type="radio"]:checked) {
    border-color: var(--accent-color);
    background: rgba(0, 122, 204, 0.15);
}

/* 語言選擇器現代化樣式 - 保留用於兼容性 */
.language-selector-modern {
    display: flex;
    align-items: center;
    gap: 10px;
}

.language-options {
    display: flex;
    gap: 12px;
}

.language-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 12px 16px;
    background: var(--bg-primary);
    border: 2px solid var(--border-color);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 80px;
}

.language-option:hover {
    border-color: var(--accent-color);
    background: rgba(0, 122, 204, 0.1);
}

.language-option.active {
    border-color: var(--accent-color);
    background: var(--accent-color);
    color: white;
}

.language-flag {
    font-size: 24px;
    margin-bottom: 4px;
}

.language-name {
    font-size: 12px;
    font-weight: 500;
    text-align: center;
}

.language-option.active .language-name {
    color: white;
}

/* 語言選擇器下拉選單樣式 */
.language-selector-dropdown {
    display: flex;
    align-items: center;
    gap: 10px;
}

.language-setting-select {
    background: var(--bg-primary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 14px;
    min-width: 140px;
    transition: border-color 0.3s ease;
    cursor: pointer;
}

.language-setting-select:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.2);
}

.language-setting-select:hover {
    border-color: var(--accent-color);
}

/* 表單元素樣式 */
.input-group {
    margin-bottom: 20px;
    width: 100%;
}

.input-label {
    display: block;
    font-weight: 500;
    margin-bottom: 8px;
    color: var(--text-primary);
}

.text-input,
.command-input {
    width: 100%;
    max-width: 100%;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 12px;
    color: var(--text-primary);
    font-size: 14px;
    line-height: 1.5;
    resize: vertical;
    min-height: 220px;
    font-family: inherit;
    transition: border-color 0.3s ease;
    box-sizing: border-box;
}

.text-input:focus,
.command-input:focus {
    outline: none;
    border-color: var(--accent-color);
}

.command-input {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    min-height: 80px;
}

/* 新增：單行命令輸入框樣式 */
.command-input-line {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 14px;
    border: 1px solid var(--border-color);
    transition: border-color 0.3s ease;
}

.command-input-line:focus {
    outline: none;
    border-color: var(--accent-color);
}

/* 命令輸出區域 */
.command-output {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 12px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.4;
    color: var(--text-primary);
    white-space: pre-wrap;
    overflow-y: auto;
    height: 320px;
    width: 100%;
    box-sizing: border-box;
    /* 添加 terminal 風格 */
    background: #0f0f0f;
    border: 2px solid var(--border-color);
    color: #00ff00;
    text-shadow: 0 0 5px #00ff00;
    /* 確保尺寸固定 */
    flex-shrink: 0;
    resize: none;
}

/* Terminal 提示符樣式 */
.terminal-prompt {
    color: var(--accent-color);
    font-weight: bold;
}

/* 按鈕樣式 */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-primary {
    background: var(--accent-color);
    color: white;
}

.btn-primary:hover {
    background: var(--accent-hover);
}

.btn-secondary {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--surface-color);
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-success:hover {
    background: #45a049;
}

/* 底部操作按鈕 */
.footer-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 20px;
    border-top: 1px solid var(--border-color);
    background: var(--bg-secondary);
    border-radius: 0 0 8px 8px;
    margin-top: auto;
}

/* 主內容區域 */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: row;
    max-width: 100%;
    overflow: hidden;
    gap: 0;
    min-height: 0; /* 允許內容自然收縮 */
}

.main-content-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 0;
    min-height: 0; /* 允許內容自然收縮 */
    padding: 12px;
    overflow: hidden;
    transition: all 0.6s cubic-bezier(0.4, 0.0, 0.2, 1);
    background: var(--bg-primary);
    border-radius: 8px 0 0 0;
    box-shadow: -2px 0 8px rgba(0, 0, 0, 0.05);
}

/* 當會話面板收合時，主內容區域擴展 */
.main-content.panel-collapsed .main-content-area {
    margin-left: 0;
    border-radius: 8px 0 0 0;
    box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);
}

/* 分頁樣式 */
.tabs {
    border-bottom: 2px solid var(--border-color);
    margin-bottom: 12px;
}

.tab-buttons {
    display: flex;
    gap: 2px;
}

.tab-button {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    padding: 10px 16px;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 500;
    border-radius: 4px 4px 0 0;
}

.tab-button.active {
    color: var(--accent-color);
    border-bottom-color: var(--accent-color);
    background: var(--bg-tertiary);
}

.tab-button:hover:not(.active) {
    color: var(--text-primary);
    background: rgba(255, 255, 255, 0.05);
}

.tab-button.hidden {
    display: none;
}

/* 分頁內容 */
.tab-content {
    display: none;
    flex: 1;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 20px;
    overflow-y: auto;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    min-height: 0; /* 移除固定高度，使用 flex 自適應 */
}

.tab-content.active {
    display: flex;
    flex-direction: column;
}

/* 分割器樣式（用於合併模式） */
.splitter-container {
    display: flex;
    flex-direction: column;
    flex: 1;
    gap: 8px;
}

.splitter-section {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 16px;
    transition: all 0.3s ease;
}

.splitter-handle {
    height: 8px;
    background: var(--border-color);
    border-radius: 4px;
    cursor: row-resize;
    transition: background 0.3s ease;
    margin: 4px 0;
}

.splitter-handle:hover {
    background: var(--accent-color);
}

/* 響應式設計 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    .header-content {
        flex-direction: column;
        gap: 10px;
    }

    .tab-buttons {
        flex-wrap: wrap;
    }

    /* 小屏幕下調整命令輸出區域高度 */
    .command-output {
        height: 250px;
    }
}

/* 更小屏幕的調整 */
@media (max-width: 480px) {
    .command-output {
        height: 200px;
        font-size: 12px;
    }
}

/* ===== 圖片上傳組件樣式 ===== */

/* 圖片上傳區域基礎樣式 */
.image-upload-area {
    border: 2px dashed var(--border-color);
    border-radius: 8px;
    background: var(--bg-tertiary);
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.image-upload-area:hover {
    border-color: var(--accent-color);
    background: rgba(0, 122, 204, 0.05);
}

/* 拖放狀態樣式 */
.image-upload-area.dragover {
    border-color: var(--accent-color) !important;
    background: rgba(0, 122, 204, 0.1) !important;
    transform: scale(1.02);
    box-shadow: 0 4px 12px rgba(0, 122, 204, 0.2);
}

/* 上傳提示文字 */
.image-upload-area > div:first-child {
    color: var(--text-secondary);
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 12px;
}

.image-upload-area > div:first-child small {
    color: var(--text-secondary);
    opacity: 0.8;
    font-size: 12px;
}

/* 禁用狀態 */
.image-upload-area.disabled {
    opacity: 0.5;
    pointer-events: none;
    cursor: not-allowed;
}

/* 圖片預覽容器 */
.image-preview-container {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-top: 12px;
    justify-content: center;
    /* 添加 padding 為刪除按鈕留出空間 */
    padding: 8px;
}

.image-preview-container:empty {
    display: none;
}

/* 圖片預覽項目 */
.image-preview-item {
    position: relative;
    display: inline-block;
    border-radius: 6px;
    /* 移除 overflow: hidden 以確保刪除按鈕完全顯示 */
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
    /* 添加一些 margin 為刪除按鈕留出空間 */
    margin: 10px;
}

.image-preview-item:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.image-preview-item img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    display: block;
    border-radius: 6px;
}

/* 圖片刪除按鈕 */
.image-remove-btn {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--error-color);
    color: white;
    border: none;
    cursor: pointer;
    font-size: 12px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.image-remove-btn:hover {
    background: #d32f2f;
    transform: scale(1.1);
}





.image-setting-label {
    font-size: 12px;
    color: var(--text-primary);
    font-weight: 500;
}

/* 圖片設定下拉選單 */
.image-setting-select {
    background: var(--bg-primary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
    min-width: 100px;
    transition: border-color 0.3s ease;
}

.image-setting-select:focus {
    outline: none;
    border-color: var(--accent-color);
}

/* 自定義複選框樣式 */
.image-setting-checkbox-container {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-size: 12px;
    color: var(--text-primary);
}

.image-setting-checkbox {
    appearance: none;
    width: 16px;
    height: 16px;
    border: 2px solid var(--border-color);
    border-radius: 3px;
    background: var(--bg-primary);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.image-setting-checkbox:checked {
    background: var(--accent-color);
    border-color: var(--accent-color);
}

.image-setting-checkbox:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 10px;
    font-weight: bold;
}

.image-setting-checkbox:focus {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}

/* 圖片設定幫助文字 */
.image-setting-help {
    color: var(--text-secondary);
    font-size: 11px;
    margin-left: 8px;
}

.image-setting-help-text {
    color: var(--text-secondary);
    font-size: 11px;
    line-height: 1.4;
    margin-top: 8px;
    padding: 8px;
    background: rgba(0, 122, 204, 0.05);
    border-radius: 4px;
    border-left: 3px solid var(--accent-color);
}

/* 相容性提示樣式 */
.compatibility-hint {
    background: rgba(255, 152, 0, 0.1);
    border: 1px solid var(--warning-color);
    border-radius: 6px;
    padding: 8px 12px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: var(--warning-color);
}

.compatibility-hint-btn {
    background: var(--warning-color);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 11px;
    cursor: pointer;
    transition: background 0.3s ease;
    font-weight: 500;
}

.compatibility-hint-btn:hover {
    background: #f57c00;
}

/* 自動刷新控制項樣式 */
.section-header-with-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

/* AI 工作摘要標題樣式 */
h3.combined-section-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}



/* Combined 標籤頁提交按鈕樣式 */
.combined-submit-btn {
    flex-shrink: 0;
    padding: 8px 16px;
    font-size: 13px;
    white-space: nowrap;
}

/* 回饋標題容器樣式 */
.feedback-title-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    gap: 16px;
}

.feedback-title-container h3.combined-section-title {
    margin: 0;
    flex: 1;
}

.feedback-title-container .combined-submit-btn {
    flex-shrink: 0;
    padding: 8px 16px;
    font-size: 13px;
    white-space: nowrap;
}

/* 響應式設計 - 小螢幕時垂直排列 */
@media (max-width: 768px) {
    .feedback-title-container {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .feedback-title-container .combined-submit-btn {
        align-self: flex-end;
        width: auto;
    }
}

/* Placeholder 文本換行修復 - 全局樣式 */
textarea::placeholder,
#combinedFeedbackText::placeholder {
    white-space: pre-line !important;
    line-height: 1.4 !important;
    font-family: inherit !important;
    color: var(--text-secondary) !important;
    opacity: 0.7 !important;
}

/* 跨瀏覽器兼容的 placeholder 樣式 */
textarea::-webkit-input-placeholder,
#combinedFeedbackText::-webkit-input-placeholder {
    white-space: pre-line !important;
    line-height: 1.4 !important;
    font-family: inherit !important;
    color: var(--text-secondary) !important;
    opacity: 0.7 !important;
}

textarea::-moz-placeholder,
#combinedFeedbackText::-moz-placeholder {
    white-space: pre-line !important;
    line-height: 1.4 !important;
    font-family: inherit !important;
    color: var(--text-secondary) !important;
    opacity: 0.7 !important;
}

textarea:-ms-input-placeholder,
#combinedFeedbackText:-ms-input-placeholder {
    white-space: pre-line !important;
    line-height: 1.4 !important;
    font-family: inherit !important;
    color: var(--text-secondary) !important;
    opacity: 0.7 !important;
}

/* ===== 自動提交功能樣式 ===== */

/* 自動提交狀態按鈕 */
.auto-submit-status-btn {
    padding: 6px 10px;
    font-size: 12px;
    border-radius: 4px;
    border: 1px solid var(--border-color);
    background: var(--bg-tertiary);
    color: var(--text-primary);
    cursor: default;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    white-space: nowrap;
    min-width: fit-content;
}

.auto-submit-status-btn.enabled {
    background: rgba(76, 175, 80, 0.1);
    color: var(--success-color);
    border: 1px solid var(--success-color);
}

.auto-submit-status-btn.disabled {
    background: rgba(158, 158, 158, 0.1);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.auto-submit-status-btn span:first-child {
    font-size: 14px;
    font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", "Segoe UI Symbol", "Android Emoji", "EmojiSymbols", sans-serif;
    line-height: 1;
    display: inline-block;
    vertical-align: middle;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.auto-submit-status-btn .button-text {
    font-size: 12px;
}

/* 自動提交設定控制項 */
.setting-control {
    display: flex;
    align-items: center;
    gap: 8px;
}

.form-input {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    color: var(--text-primary);
    padding: 8px 12px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.form-input:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.2);
}

.form-select {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    color: var(--text-primary);
    padding: 8px 12px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.form-select:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.2);
}

.form-select:hover {
    border-color: var(--accent-color);
}

.input-suffix {
    color: var(--text-secondary);
    font-size: 14px;
    margin-left: 4px;
}

/* 自動提交切換按鈕 */
.toggle-btn {
    position: relative;
    width: 50px;
    height: 24px;
    background: var(--border-color);
    border: none;
    border-radius: 24px;
    cursor: pointer;
    transition: all 0.3s ease;
    outline: none;
}

.toggle-btn.active {
    background: var(--accent-color);
}

.toggle-btn .toggle-slider {
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.toggle-btn.active .toggle-slider {
    transform: translateX(26px);
}

.toggle-btn:hover {
    box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.2);
}

.toggle-btn:focus {
    box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.4);
}

/* 自動提交提示詞樣式 */
.auto-submit-prompt {
    border-left: 4px solid var(--accent-color) !important;
    background: rgba(0, 122, 204, 0.05) !important;
}

.auto-submit-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    background: var(--accent-color);
    color: white;
    border-radius: 50%;
    font-size: 12px;
    margin-right: 8px;
    font-weight: bold;
}

.auto-submit-status {
    color: var(--accent-color);
    font-weight: 500;
    font-size: 12px;
}

/* 提示詞操作按鈕樣式 */
.prompt-action-btn {
    padding: 6px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: var(--bg-primary);
    color: var(--text-primary);
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
    margin-left: 4px;
}

.prompt-action-btn:hover {
    border-color: var(--accent-color);
    background: rgba(0, 122, 204, 0.1);
}

.prompt-action-btn.auto-submit-btn {
    font-size: 14px;
    padding: 4px 8px;
}

.prompt-action-btn.auto-submit-btn.active {
    background: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
}

.prompt-action-btn.edit-btn:hover {
    border-color: var(--warning-color);
    background: rgba(255, 152, 0, 0.1);
    color: var(--warning-color);
}

.prompt-action-btn.delete-btn:hover {
    border-color: var(--error-color);
    background: rgba(244, 67, 54, 0.1);
    color: var(--error-color);
}

/* ===== Markdown 內容樣式 ===== */

/* AI 摘要區域的 Markdown 樣式 - 基於 marked.js 官方樣式 */
/* 移除舊的衝突樣式，使用下方的 VSCode 風格樣式 */

/* Markdown 強調樣式 */
#summaryContent strong, #combinedSummaryContent strong {
    font-weight: 600;
    color: var(--text-primary);
}

#summaryContent em, #combinedSummaryContent em {
    font-style: italic;
    color: var(--text-primary);
}

/* Markdown 刪除線樣式 */
#summaryContent del, #summaryContent s,
#combinedSummaryContent del, #combinedSummaryContent s {
    text-decoration: line-through;
    color: var(--text-secondary);
    opacity: 0.7;
}

/* Markdown 程式碼樣式 - 基於 marked.js 官方樣式 */
#summaryContent code, #combinedSummaryContent code {
    padding: 0.2em 0.4em !important;
    margin: 0 !important;
    font-size: 85% !important;
    background-color: rgba(175, 184, 193, 0.2) !important;
    border-radius: 3px !important;
    font-family: "SFMono-Regular",Consolas,"Liberation Mono",Menlo,Courier,monospace !important;
    color: var(--text-primary) !important;
}

#summaryContent pre, #combinedSummaryContent pre {
    font-family: "SFMono-Regular",Consolas,"Liberation Mono",Menlo,Courier,monospace !important;
    padding: 16px !important;
    overflow: auto !important;
    font-size: 85% !important;
    line-height: 1.45 !important;
    background-color: rgba(175, 184, 193, 0.1) !important;
    border-radius: 3px !important;
    margin: 16px 0 !important;
    border: none !important;
}

#summaryContent pre code, #combinedSummaryContent pre code {
    background: none !important;
    border: none !important;
    padding: 0 !important;
    margin: 0 !important;
    font-size: 100% !important;
    color: var(--text-primary) !important;
    display: block !important;
    overflow: auto !important;
}

/* 移除舊的列表樣式，使用下方的 VSCode 風格樣式 */

/* Markdown 引用區塊樣式 */
#summaryContent blockquote, #combinedSummaryContent blockquote {
    border-left: 4px solid var(--accent-color);
    margin: 1em 0;
    padding: 0.5em 1em;
    background: rgba(0, 122, 204, 0.05);
    color: var(--text-secondary);
    font-style: italic;
}

/* Markdown 連結樣式 - 基於 marked.js 官方樣式 */
#summaryContent a, #combinedSummaryContent a {
    color: #0366d6;
    text-decoration: none;
}

#summaryContent a:hover, #combinedSummaryContent a:hover {
    text-decoration: underline;
}

/* Markdown 水平線樣式 - 基於 marked.js 官方樣式 */
#summaryContent hr, #combinedSummaryContent hr {
    border: none;
    border-top: 1px solid var(--border-color);
    margin: 24px 0;
    height: 0;
}

/* 確保第一個元素沒有上邊距 */
#summaryContent > *:first-child, #combinedSummaryContent > *:first-child {
    margin-top: 0;
}

/* 確保最後一個元素沒有下邊距 */
#summaryContent > *:last-child, #combinedSummaryContent > *:last-child {
    margin-bottom: 0;
}

/* Markdown 內容樣式 - VSCode 風格極緊湊樣式 */
#summaryContent, #combinedSummaryContent {
    font-family: -apple-system,BlinkMacSystemFont,"Segoe UI",Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol" !important;
    font-size: 14px !important;
    line-height: 1.1 !important;
    word-wrap: break-word !important;
}

/* 強制覆蓋所有子元素的行高和間距 */
#summaryContent *, #combinedSummaryContent * {
    line-height: 1.1 !important;
}

#summaryContent p, #combinedSummaryContent p {
    margin: 0 !important;
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    line-height: 1.1 !important;
    font-size: 14px !important;
    padding: 0 !important;
}

/* 標題樣式 - VSCode 風格含底線 */
#summaryContent h1, #summaryContent h2, #summaryContent h3,
#summaryContent h4, #summaryContent h5, #summaryContent h6,
#combinedSummaryContent h1, #combinedSummaryContent h2, #combinedSummaryContent h3,
#combinedSummaryContent h4, #combinedSummaryContent h5, #combinedSummaryContent h6 {
    color: var(--text-primary) !important;
    font-weight: 600 !important;
}

#summaryContent h1, #combinedSummaryContent h1 {
    margin: 0.3em 0 0.1em 0 !important;
    line-height: 1.1 !important;
    font-size: 1.8em !important;
    border-bottom: 2px solid var(--border-color) !important;
    padding-bottom: 6px !important;
}

#summaryContent h2, #combinedSummaryContent h2 {
    margin: 0.25em 0 0.08em 0 !important;
    line-height: 1.1 !important;
    font-size: 1.5em !important;
    border-bottom: 1px solid var(--border-color) !important;
    padding-bottom: 4px !important;
}

#summaryContent h3, #combinedSummaryContent h3 {
    margin: 0.2em 0 0.03em 0 !important;
    line-height: 1.1 !important;
    font-size: 1.3em !important;
}

#summaryContent h4, #combinedSummaryContent h4 {
    margin: 0.15em 0 0.02em 0 !important;
    line-height: 1.1 !important;
    font-size: 1.1em !important;
}

#summaryContent h5, #combinedSummaryContent h5,
#summaryContent h6, #combinedSummaryContent h6 {
    margin: 0.1em 0 0.02em 0 !important;
    line-height: 1.1 !important;
    font-size: 1em !important;
}

/* 列表樣式 - VSCode 風格極緊湊 */
#summaryContent ul, #combinedSummaryContent ul,
#summaryContent ol, #combinedSummaryContent ol {
    margin: 0 !important;
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    padding: 0 !important;
    padding-left: 1.2em !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
}

#summaryContent li, #combinedSummaryContent li {
    margin: 0 !important;
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    line-height: 1.1 !important;
    padding: 0 !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
    font-size: 14px !important;
}

#summaryContent li > ul, #combinedSummaryContent li > ul,
#summaryContent li > ol, #combinedSummaryContent li > ol {
    margin: 0 !important;
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    padding: 0 !important;
    padding-left: 1.0em !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
}

/* 強制段落後的列表緊密貼合 */
#summaryContent p + ul, #combinedSummaryContent p + ul,
#summaryContent p + ol, #combinedSummaryContent p + ol,
#summaryContent h1 + ul, #combinedSummaryContent h1 + ul,
#summaryContent h2 + ul, #combinedSummaryContent h2 + ul,
#summaryContent h3 + ul, #combinedSummaryContent h3 + ul,
#summaryContent h4 + ul, #combinedSummaryContent h4 + ul,
#summaryContent h1 + ol, #combinedSummaryContent h1 + ol,
#summaryContent h2 + ol, #combinedSummaryContent h2 + ol,
#summaryContent h3 + ol, #combinedSummaryContent h3 + ol,
#summaryContent h4 + ol, #combinedSummaryContent h4 + ol {
    margin-top: 0 !important;
    padding-top: 0 !important;
}

#summaryContent blockquote, #combinedSummaryContent blockquote {
    margin: 0.05em 0 !important;
    padding: 0.1em 0.5em !important;
    line-height: 1.1 !important;
    font-size: 14px !important;
}

#summaryContent pre, #combinedSummaryContent pre {
    margin: 0.05em 0 !important;
    line-height: 1.1 !important;
    font-size: 13px !important;
}

#summaryContent code, #combinedSummaryContent code {
    line-height: 1.1 !important;
    font-size: 13px !important;
}

/* Markdown 表格樣式 - 適應主題顏色 */
#summaryContent table, #combinedSummaryContent table {
    border-spacing: 0 !important;
    border-collapse: collapse !important;
    display: table !important;
    width: 100% !important;
    margin: 0.5em 0 !important;
    font-variant: tabular-nums !important;
    border: 1px solid var(--border-color) !important;
    border-radius: 6px !important;
    overflow: hidden !important;
    background-color: var(--bg-primary) !important;
}

#summaryContent thead, #combinedSummaryContent thead {
    display: table-header-group !important;
}

#summaryContent tbody, #combinedSummaryContent tbody {
    display: table-row-group !important;
}

#summaryContent tr, #combinedSummaryContent tr {
    display: table-row !important;
    background-color: var(--bg-primary) !important;
    border-top: 1px solid var(--border-color) !important;
}

#summaryContent tr:nth-child(2n), #combinedSummaryContent tr:nth-child(2n) {
    background-color: var(--bg-secondary) !important;
}

#summaryContent td, #summaryContent th,
#combinedSummaryContent td, #combinedSummaryContent th {
    display: table-cell !important;
    padding: 4px 8px !important;
    border: 1px solid var(--border-color) !important;
    text-align: left !important;
    vertical-align: top !important;
    color: var(--text-color) !important;
    line-height: 1.3 !important;
}

#summaryContent th, #combinedSummaryContent th {
    font-weight: 600 !important;
    background-color: var(--bg-secondary) !important;
    border-bottom: 2px solid var(--border-color) !important;
}

#summaryContent td > *:last-child, #combinedSummaryContent td > *:last-child {
    margin-bottom: 0 !important;
}

/* 會話超時倒數樣式 */
.session-timeout-display {
    background: rgba(255, 193, 7, 0.1);
    border: 1px solid rgba(255, 193, 7, 0.3);
    border-radius: 4px;
    padding: 2px 8px;
}

.session-timeout-display .timeout-label {
    font-size: 12px;
    color: var(--text-secondary);
    margin-right: 4px;
}

.session-timeout-display .countdown-timer {
    color: var(--text-primary);
}

.session-timeout-display.countdown-warning .countdown-timer {
    color: var(--error-color);
    animation: pulse 1s ease-in-out infinite;
}

/* 自動提交倒數樣式 */
.auto-submit-display {
    background: rgba(76, 175, 80, 0.1);
    border: 1px solid rgba(76, 175, 80, 0.3);
    border-radius: 4px;
    padding: 2px 8px;
}

.auto-submit-display .countdown-label {
    font-size: 12px;
    color: var(--text-secondary);
    margin-right: 4px;
}

.auto-submit-display .countdown-timer {
    color: var(--text-primary);
    font-weight: 500;
}

/* 倒數控制按鈕 */
.countdown-control-btn {
    background: transparent;
    border: none;
    padding: 2px 6px;
    margin-left: 4px;
    cursor: pointer;
    font-size: 14px;
    color: var(--text-secondary);
    transition: color 0.2s ease;
}

.countdown-control-btn:hover {
    color: var(--text-primary);
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.6; }
    100% { opacity: 1; }
}

/* 自動執行命令設定樣式 */
.command-auto-settings {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 16px;
    margin: 16px 0;
}

.command-auto-settings .settings-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.command-auto-settings .settings-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
}

.command-auto-settings .settings-content {
    transition: opacity 0.3s ease;
}

.command-auto-settings .settings-content.disabled {
    opacity: 0.5;
    pointer-events: none;
}

.auto-command-item {
    margin-bottom: 20px;
    padding: 12px;
    background: var(--bg-tertiary);
    border-radius: 6px;
    border: 1px solid var(--border-color);
}

.auto-command-item:last-child {
    margin-bottom: 0;
}

.command-label {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-primary);
}

.command-icon {
    font-size: 18px;
}

.command-input-wrapper {
    display: flex;
    align-items: center;
    margin: 8px 0;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 0 12px;
    transition: border-color 0.3s ease;
}

.command-input-wrapper:focus-within {
    border-color: var(--accent-color);
}

.command-prefix {
    color: var(--accent-color);
    font-weight: bold;
    margin-right: 8px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

.auto-command-input {
    flex: 1;
    border: none;
    background: transparent;
    padding: 8px 0;
    color: var(--text-primary);
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 14px;
    outline: none;
}

.command-actions {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-top: 8px;
}

.btn-test {
    background: var(--info-color);
    color: white;
    border: none;
    padding: 4px 12px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.btn-test:hover {
    background: #1976d2;
}

.command-hint {
    color: var(--text-secondary);
    font-size: 12px;
}

.btn-small {
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

